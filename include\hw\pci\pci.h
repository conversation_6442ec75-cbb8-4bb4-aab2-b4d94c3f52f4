#ifndef QEMU_PCI_H
#define QEMU_PCI_H

#include "system/memory.h"
#include "system/dma.h"
#include "system/host_iommu_device.h"

/* PCI includes legacy ISA access.  */
#include "hw/isa/isa.h"

/* 全局变量：指示PCI总线是否可用 */
extern bool pci_available;

/* PCI bus */

/* 将设备槽位(slot)和功能号(func)组合成设备功能号(devfn) */
/* slot: 0-31 (5位), func: 0-7 (3位) */
#define PCI_DEVFN(slot, func)   ((((slot) & 0x1f) << 3) | ((func) & 0x07))
/* 从BDF(Bus/Device/Function)中提取总线号 */
#define PCI_BUS_NUM(x)          (((x) >> 8) & 0xff)
/* 从设备功能号(devfn)中提取设备槽位号 */
#define PCI_SLOT(devfn)         (((devfn) >> 3) & 0x1f)
/* 从设备功能号(devfn)中提取功能号 */
#define PCI_FUNC(devfn)         ((devfn) & 0x07)
/* 构建BDF标识符：将总线号和设备功能号组合 */
#define PCI_BUILD_BDF(bus, devfn)     (((bus) << 8) | (devfn))
/* 从BDF中提取设备功能号部分 */
#define PCI_BDF_TO_DEVFN(x)     ((x) & 0xff)
/* PCI总线最大数量：256个总线 */
#define PCI_BUS_MAX             256
/* 设备功能号最大值：256 (32个设备 × 8个功能) */
#define PCI_DEVFN_MAX           256
/* 每个总线上最大设备槽位数：32个 */
#define PCI_SLOT_MAX            32
/* 每个设备最大功能数：8个 */
#define PCI_FUNC_MAX            8

/* 构建SBDF(Segment/Bus/Device/Function)标识符 */
/* 包含段号(16位)、总线号、设备号和功能号的完整地址 */
#define PCI_SBDF(seg, bus, dev, func) \
            ((((uint32_t)(seg)) << 16) | \
            (PCI_BUILD_BDF(bus, PCI_DEVFN(dev, func))))

/* 从Linux的pci_ids.h文件中引入类别、厂商和设备ID定义 */
#include "hw/pci/pci_ids.h"

/* QEMU特定的厂商和设备ID定义 */

/* IBM公司 (厂商ID: 0x1014) */
#define PCI_DEVICE_ID_IBM_440GX          0x027f  /* IBM 440GX芯片组设备ID */
#define PCI_DEVICE_ID_IBM_OPENPIC2       0xffff  /* IBM OpenPIC2中断控制器设备ID */

/* 日立公司 (厂商ID: 0x1054) */
#define PCI_VENDOR_ID_HITACHI            0x1054  /* 日立公司厂商ID */
#define PCI_DEVICE_ID_HITACHI_SH7751R    0x350e  /* 日立SH7751R处理器设备ID */

/* 苹果公司 (厂商ID: 0x106b) */
#define PCI_DEVICE_ID_APPLE_343S1201     0x0010  /* 苹果343S1201芯片设备ID */
#define PCI_DEVICE_ID_APPLE_UNI_N_I_PCI  0x001e  /* 苹果UniNorth内部PCI桥设备ID */
#define PCI_DEVICE_ID_APPLE_UNI_N_PCI    0x001f  /* 苹果UniNorth PCI桥设备ID */
#define PCI_DEVICE_ID_APPLE_UNI_N_KEYL   0x0022  /* 苹果UniNorth KeyLargo设备ID */
#define PCI_DEVICE_ID_APPLE_IPID_USB     0x003f  /* 苹果IPID USB控制器设备ID */

/* 瑞昱公司 (厂商ID: 0x10ec) */
#define PCI_DEVICE_ID_REALTEK_8029       0x8029  /* 瑞昱RTL8029网卡设备ID */

/* 赛灵思公司 (厂商ID: 0x10ee) */
#define PCI_DEVICE_ID_XILINX_XC2VP30     0x0300  /* 赛灵思XC2VP30 FPGA设备ID */

/* 迈威尔公司 (厂商ID: 0x11ab) */
#define PCI_DEVICE_ID_MARVELL_GT6412X    0x4620  /* 迈威尔GT6412X芯片设备ID */

/* QEMU/Bochs VGA (厂商ID: 0x1234) */
#define PCI_VENDOR_ID_QEMU               0x1234  /* QEMU模拟器厂商ID */
#define PCI_DEVICE_ID_QEMU_VGA           0x1111  /* QEMU VGA显卡设备ID */
#define PCI_DEVICE_ID_QEMU_IPMI          0x1112  /* QEMU IPMI设备ID */

/* VMware公司 (厂商ID: 0x15ad) */
#define PCI_VENDOR_ID_VMWARE             0x15ad  /* VMware厂商ID */
#define PCI_DEVICE_ID_VMWARE_SVGA2       0x0405  /* VMware SVGA2显卡设备ID */
#define PCI_DEVICE_ID_VMWARE_SVGA        0x0710  /* VMware SVGA显卡设备ID */
#define PCI_DEVICE_ID_VMWARE_NET         0x0720  /* VMware网络适配器设备ID */
#define PCI_DEVICE_ID_VMWARE_SCSI        0x0730  /* VMware SCSI控制器设备ID */
#define PCI_DEVICE_ID_VMWARE_PVSCSI      0x07C0  /* VMware半虚拟化SCSI控制器设备ID */
#define PCI_DEVICE_ID_VMWARE_IDE         0x1729  /* VMware IDE控制器设备ID */
#define PCI_DEVICE_ID_VMWARE_VMXNET3     0x07B0  /* VMware VMXNET3网络适配器设备ID */

/* 英特尔公司 (厂商ID: 0x8086) */
#define PCI_DEVICE_ID_INTEL_82551IT      0x1209  /* 英特尔82551IT网卡设备ID */
#define PCI_DEVICE_ID_INTEL_82557        0x1229  /* 英特尔82557网卡设备ID */
#define PCI_DEVICE_ID_INTEL_82801IR      0x2922  /* 英特尔82801IR芯片组设备ID */

/* Red Hat / Qumranet (用于QEMU) -- 参见pci-ids.txt */
#define PCI_VENDOR_ID_REDHAT_QUMRANET    0x1af4  /* Red Hat/Qumranet厂商ID */
#define PCI_SUBVENDOR_ID_REDHAT_QUMRANET 0x1af4  /* Red Hat/Qumranet子厂商ID */
#define PCI_SUBDEVICE_ID_QEMU            0x1100  /* QEMU子设备ID */

/* 传统virtio-pci设备 */
#define PCI_DEVICE_ID_VIRTIO_NET         0x1000  /* virtio网络设备ID */
#define PCI_DEVICE_ID_VIRTIO_BLOCK       0x1001  /* virtio块设备ID */
#define PCI_DEVICE_ID_VIRTIO_BALLOON     0x1002  /* virtio内存气球设备ID */
#define PCI_DEVICE_ID_VIRTIO_CONSOLE     0x1003  /* virtio控制台设备ID */
#define PCI_DEVICE_ID_VIRTIO_SCSI        0x1004  /* virtio SCSI设备ID */
#define PCI_DEVICE_ID_VIRTIO_RNG         0x1005  /* virtio随机数生成器设备ID */
#define PCI_DEVICE_ID_VIRTIO_9P          0x1009  /* virtio 9P文件系统设备ID */
#define PCI_DEVICE_ID_VIRTIO_VSOCK       0x1012  /* virtio虚拟套接字设备ID */

/*
 * 现代virtio-pci设备的ID是自动分配的，
 * 这里不需要添加#define定义。计算方式如下：
 *
 * PCI_DEVICE_ID = PCI_DEVICE_ID_VIRTIO_10_BASE +
 *                 virtio_bus_get_vdev_id(bus)
 */
#define PCI_DEVICE_ID_VIRTIO_10_BASE     0x1040  /* 现代virtio设备ID基址 */

#define PCI_VENDOR_ID_REDHAT             0x1b36  /* Red Hat厂商ID */
#define PCI_DEVICE_ID_REDHAT_BRIDGE      0x0001  /* Red Hat桥接设备ID */
#define PCI_DEVICE_ID_REDHAT_SERIAL      0x0002  /* Red Hat串口设备ID */
#define PCI_DEVICE_ID_REDHAT_SERIAL2     0x0003  /* Red Hat串口设备ID (第二个) */
#define PCI_DEVICE_ID_REDHAT_SERIAL4     0x0004  /* Red Hat串口设备ID (第四个) */
#define PCI_DEVICE_ID_REDHAT_TEST        0x0005  /* Red Hat测试设备ID */
#define PCI_DEVICE_ID_REDHAT_ROCKER      0x0006  /* Red Hat Rocker交换机设备ID */
#define PCI_DEVICE_ID_REDHAT_SDHCI       0x0007  /* Red Hat SD主机控制器设备ID */
#define PCI_DEVICE_ID_REDHAT_PCIE_HOST   0x0008  /* Red Hat PCIe主机桥设备ID */
#define PCI_DEVICE_ID_REDHAT_PXB         0x0009  /* Red Hat PCI扩展桥设备ID */
#define PCI_DEVICE_ID_REDHAT_BRIDGE_SEAT 0x000a  /* Red Hat桥接座设备ID */
#define PCI_DEVICE_ID_REDHAT_PXB_PCIE    0x000b  /* Red Hat PCIe扩展桥设备ID */
#define PCI_DEVICE_ID_REDHAT_PCIE_RP     0x000c  /* Red Hat PCIe根端口设备ID */
#define PCI_DEVICE_ID_REDHAT_XHCI        0x000d  /* Red Hat xHCI USB控制器设备ID */
#define PCI_DEVICE_ID_REDHAT_PCIE_BRIDGE 0x000e  /* Red Hat PCIe桥接设备ID */
#define PCI_DEVICE_ID_REDHAT_MDPY        0x000f  /* Red Hat MDPY显示设备ID */
#define PCI_DEVICE_ID_REDHAT_NVME        0x0010  /* Red Hat NVMe存储设备ID */
#define PCI_DEVICE_ID_REDHAT_PVPANIC     0x0011  /* Red Hat PV Panic设备ID */
#define PCI_DEVICE_ID_REDHAT_ACPI_ERST   0x0012  /* Red Hat ACPI ERST设备ID */
#define PCI_DEVICE_ID_REDHAT_UFS         0x0013  /* Red Hat UFS存储设备ID */
#define PCI_DEVICE_ID_REDHAT_RISCV_IOMMU 0x0014  /* Red Hat RISC-V IOMMU设备ID */
#define PCI_DEVICE_ID_REDHAT_QXL         0x0100  /* Red Hat QXL显卡设备ID */

/* PCI总线地址格式化宏，用于打印64位地址 */
#define FMT_PCIBUS                      PRIx64

/* PCI总线地址类型定义，使用64位无符号整数 */
typedef uint64_t pcibus_t;

/* PCI主机设备地址结构体 */
struct PCIHostDeviceAddress {
    unsigned int domain;    /* PCI域号 */
    unsigned int bus;       /* 总线号 */
    unsigned int slot;      /* 设备槽位号 */
    unsigned int function;  /* 功能号 */
};

/* PCI配置空间写入函数类型定义 */
typedef void PCIConfigWriteFunc(PCIDevice *pci_dev,
                                uint32_t address, uint32_t data, int len);
/* PCI配置空间读取函数类型定义 */
typedef uint32_t PCIConfigReadFunc(PCIDevice *pci_dev,
                                   uint32_t address, int len);
/* PCI I/O区域映射函数类型定义 */
typedef void PCIMapIORegionFunc(PCIDevice *pci_dev, int region_num,
                                pcibus_t addr, pcibus_t size, int type);
/* PCI设备注销函数类型定义 */
typedef void PCIUnregisterFunc(PCIDevice *pci_dev);

/* MSI中断触发函数类型定义 */
typedef void MSITriggerFunc(PCIDevice *dev, MSIMessage msg);
/* MSI消息准备函数类型定义 */
typedef MSIMessage MSIPrepareMessageFunc(PCIDevice *dev, unsigned vector);
/* MSI-X消息准备函数类型定义 */
typedef MSIMessage MSIxPrepareMessageFunc(PCIDevice *dev, unsigned vector);

/* PCI I/O区域结构体定义 */
typedef struct PCIIORegion {
    pcibus_t addr; /* 当前PCI映射地址，-1表示未映射 */
#define PCI_BAR_UNMAPPED (~(pcibus_t)0)  /* 未映射的BAR标记 */
    pcibus_t size;              /* 区域大小 */
    uint8_t type;               /* 区域类型 */
    MemoryRegion *memory;       /* 内存区域指针 */
    MemoryRegion *address_space; /* 地址空间指针 */
} PCIIORegion;

#define PCI_ROM_SLOT 6      /* ROM BAR槽位号 */
#define PCI_NUM_REGIONS 7   /* PCI区域总数 */

/* QEMU PCI VGA区域枚举 */
enum {
    QEMU_PCI_VGA_MEM,       /* VGA内存区域 */
    QEMU_PCI_VGA_IO_LO,     /* VGA低端I/O区域 */
    QEMU_PCI_VGA_IO_HI,     /* VGA高端I/O区域 */
    QEMU_PCI_VGA_NUM_REGIONS, /* VGA区域总数 */
};

/* QEMU PCI VGA区域地址和大小定义 */
#define QEMU_PCI_VGA_MEM_BASE 0xa0000   /* VGA内存基地址 */
#define QEMU_PCI_VGA_MEM_SIZE 0x20000   /* VGA内存大小 (128KB) */
#define QEMU_PCI_VGA_IO_LO_BASE 0x3b0   /* VGA低端I/O基地址 */
#define QEMU_PCI_VGA_IO_LO_SIZE 0xc     /* VGA低端I/O大小 (12字节) */
#define QEMU_PCI_VGA_IO_HI_BASE 0x3c0   /* VGA高端I/O基地址 */
#define QEMU_PCI_VGA_IO_HI_SIZE 0x20    /* VGA高端I/O大小 (32字节) */

/* 包含PCI寄存器定义 */
#include "hw/pci/pci_regs.h"

/* PCI头部类型定义 */
#define  PCI_HEADER_TYPE_MULTI_FUNCTION 0x80  /* 多功能设备标志 */

/* 标准PCI配置头部大小 */
#define PCI_CONFIG_HEADER_SIZE 0x40     /* 64字节 */
/* 标准PCI配置空间大小 */
#define PCI_CONFIG_SPACE_SIZE 0x100     /* 256字节 */
/* 标准PCIe配置空间大小：4KB */
#define PCIE_CONFIG_SPACE_SIZE  0x1000  /* 4096字节 */

#define PCI_NUM_PINS 4 /* PCI中断引脚数量：A-D */

/* cap_present字段中的位标志定义 */
enum {
    QEMU_PCI_CAP_MSI = 0x1,         /* MSI中断能力 */
    QEMU_PCI_CAP_MSIX = 0x2,        /* MSI-X中断能力 */
    QEMU_PCI_CAP_EXPRESS = 0x4,     /* PCI Express能力 */

    /* 多功能设备能力 */
#define QEMU_PCI_CAP_MULTIFUNCTION_BITNR        3
    QEMU_PCI_CAP_MULTIFUNCTION = (1 << QEMU_PCI_CAP_MULTIFUNCTION_BITNR),

    /* 命令寄存器SERR位使能 - 自QEMU v5.0起未使用 */
#define QEMU_PCI_CAP_SERR_BITNR 4
    QEMU_PCI_CAP_SERR = (1 << QEMU_PCI_CAP_SERR_BITNR),
    /* 标准热插拔控制器 */
#define QEMU_PCI_SHPC_BITNR 5
    QEMU_PCI_CAP_SHPC = (1 << QEMU_PCI_SHPC_BITNR),
#define QEMU_PCI_SLOTID_BITNR 6
    QEMU_PCI_CAP_SLOTID = (1 << QEMU_PCI_SLOTID_BITNR),  /* 槽位ID能力 */
    /* PCI Express能力 - 电源控制器存在 */
#define QEMU_PCIE_SLTCAP_PCP_BITNR 7
    QEMU_PCIE_SLTCAP_PCP = (1 << QEMU_PCIE_SLTCAP_PCP_BITNR),
    /* 端点能力中的链路活动状态始终设置 */
#define QEMU_PCIE_LNKSTA_DLLLA_BITNR 8
    QEMU_PCIE_LNKSTA_DLLLA = (1 << QEMU_PCIE_LNKSTA_DLLLA_BITNR),
#define QEMU_PCIE_EXTCAP_INIT_BITNR 9
    QEMU_PCIE_EXTCAP_INIT = (1 << QEMU_PCIE_EXTCAP_INIT_BITNR),  /* PCIe扩展能力初始化 */
#define QEMU_PCIE_CXL_BITNR 10
    QEMU_PCIE_CAP_CXL = (1 << QEMU_PCIE_CXL_BITNR),  /* CXL (Compute Express Link)能力 */
#define QEMU_PCIE_ERR_UNC_MASK_BITNR 11
    QEMU_PCIE_ERR_UNC_MASK = (1 << QEMU_PCIE_ERR_UNC_MASK_BITNR),  /* PCIe不可纠正错误掩码 */
#define QEMU_PCIE_ARI_NEXTFN_1_BITNR 12
    QEMU_PCIE_ARI_NEXTFN_1 = (1 << QEMU_PCIE_ARI_NEXTFN_1_BITNR),  /* ARI下一功能号为1 */
#define QEMU_PCIE_EXT_TAG_BITNR 13
    QEMU_PCIE_EXT_TAG = (1 << QEMU_PCIE_EXT_TAG_BITNR),  /* PCIe扩展标签能力 */
#define QEMU_PCI_CAP_PM_BITNR 14
    QEMU_PCI_CAP_PM = (1 << QEMU_PCI_CAP_PM_BITNR),  /* PCI电源管理能力 */
#define QEMU_PCI_SKIP_RESET_ON_CPR_BITNR 15
    QEMU_PCI_SKIP_RESET_ON_CPR = (1 << QEMU_PCI_SKIP_RESET_ON_CPR_BITNR),  /* CPR时跳过重置 */
};

/* PCI传统中断路由结构体 */
typedef struct PCIINTxRoute {
    enum {
        PCI_INTX_ENABLED,   /* 中断使能 */
        PCI_INTX_INVERTED,  /* 中断反转 */
        PCI_INTX_DISABLED,  /* 中断禁用 */
    } mode;                 /* 中断模式 */
    int irq;               /* 中断号 */
} PCIINTxRoute;

/* PCI传统中断路由通知函数类型定义 */
typedef void (*PCIINTxRoutingNotifier)(PCIDevice *dev);
/* MSI向量使用通知函数类型定义 */
typedef int (*MSIVectorUseNotifier)(PCIDevice *dev, unsigned int vector,
                                      MSIMessage msg);
/* MSI向量释放通知函数类型定义 */
typedef void (*MSIVectorReleaseNotifier)(PCIDevice *dev, unsigned int vector);
/* MSI向量轮询通知函数类型定义 */
typedef void (*MSIVectorPollNotifier)(PCIDevice *dev,
                                      unsigned int vector_start,
                                      unsigned int vector_end);

/* 注册PCI设备的BAR(基地址寄存器) */
void pci_register_bar(PCIDevice *pci_dev, int region_num,
                      uint8_t attr, MemoryRegion *memory);
/* 注册VGA设备的内存和I/O区域 */
void pci_register_vga(PCIDevice *pci_dev, MemoryRegion *mem,
                      MemoryRegion *io_lo, MemoryRegion *io_hi);
/* 注销VGA设备 */
void pci_unregister_vga(PCIDevice *pci_dev);
/* 获取PCI设备指定BAR的地址 */
pcibus_t pci_get_bar_addr(PCIDevice *pci_dev, int region_num);

/* 为PCI设备添加能力 */
int pci_add_capability(PCIDevice *pdev, uint8_t cap_id,
                       uint8_t offset, uint8_t size,
                       Error **errp);

/* 删除PCI设备的能力 */
void pci_del_capability(PCIDevice *pci_dev, uint8_t cap_id, uint8_t cap_size);

/* 查找PCI设备的指定能力 */
uint8_t pci_find_capability(PCIDevice *pci_dev, uint8_t cap_id);


/* 默认的PCI配置空间读取函数 */
uint32_t pci_default_read_config(PCIDevice *d,
                                 uint32_t address, int len);
/* 默认的PCI配置空间写入函数 */
void pci_default_write_config(PCIDevice *d,
                              uint32_t address, uint32_t val, int len);
/* 保存PCI设备状态 */
void pci_device_save(PCIDevice *s, QEMUFile *f);
/* 加载PCI设备状态 */
int pci_device_load(PCIDevice *s, QEMUFile *f);
/* 获取PCI设备的内存地址空间 */
MemoryRegion *pci_address_space(PCIDevice *dev);
/* 获取PCI设备的I/O地址空间 */
MemoryRegion *pci_address_space_io(PCIDevice *dev);

/*
 * 通常不应被设备使用。用于sPAPR目标平台，
 * 其中QEMU模拟固件。
 */
int pci_bar(PCIDevice *d, int reg);

/* PCI中断设置函数类型定义 */
typedef void (*pci_set_irq_fn)(void *opaque, int irq_num, int level);
/* PCI中断映射函数类型定义 */
typedef int (*pci_map_irq_fn)(PCIDevice *pci_dev, int irq_num);
/* PCI中断路由函数类型定义 */
typedef PCIINTxRoute (*pci_route_irq_fn)(void *opaque, int pin);

/* PCI总线类型定义 */
#define TYPE_PCI_BUS "PCI"
OBJECT_DECLARE_TYPE(PCIBus, PCIBusClass, PCI_BUS)
#define TYPE_PCIE_BUS "PCIE"  /* PCIe总线类型 */
#define TYPE_CXL_BUS "CXL"    /* CXL总线类型 */

/* PCI总线设备遍历函数类型定义 */
typedef void (*pci_bus_dev_fn)(PCIBus *b, PCIDevice *d, void *opaque);
/* PCI总线遍历函数类型定义 */
typedef void (*pci_bus_fn)(PCIBus *b, void *opaque);
/* PCI总线返回值函数类型定义 */
typedef void *(*pci_bus_ret_fn)(PCIBus *b, void *opaque);

/* 检查PCI总线是否为Express总线 */
bool pci_bus_is_express(const PCIBus *bus);

/* 初始化PCI根总线 */
void pci_root_bus_init(PCIBus *bus, size_t bus_size, DeviceState *parent,
                       const char *name,
                       MemoryRegion *mem, MemoryRegion *io,
                       uint8_t devfn_min, const char *typename);
/* 创建新的PCI根总线 */
PCIBus *pci_root_bus_new(DeviceState *parent, const char *name,
                         MemoryRegion *mem, MemoryRegion *io,
                         uint8_t devfn_min, const char *typename);
/* 清理PCI根总线 */
void pci_root_bus_cleanup(PCIBus *bus);
/* 设置PCI总线中断 */
void pci_bus_irqs(PCIBus *bus, pci_set_irq_fn set_irq,
                  void *irq_opaque, int nirq);
/* 映射PCI总线中断 */
void pci_bus_map_irqs(PCIBus *bus, pci_map_irq_fn map_irq);
/* 清理PCI总线中断 */
void pci_bus_irqs_cleanup(PCIBus *bus);
/* 获取PCI总线中断级别 */
int pci_bus_get_irq_level(PCIBus *bus, int irq_num);
/* 获取PCI总线槽位保留掩码 */
uint32_t pci_bus_get_slot_reserved_mask(PCIBus *bus);
/* 设置PCI总线槽位保留掩码 */
void pci_bus_set_slot_reserved_mask(PCIBus *bus, uint32_t mask);
/* 清除PCI总线槽位保留掩码 */
void pci_bus_clear_slot_reserved_mask(PCIBus *bus, uint32_t mask);
/* 为固件配置添加额外的PCI根总线 */
bool pci_bus_add_fw_cfg_extra_pci_roots(FWCfgState *fw_cfg,
                                        PCIBus *bus,
                                        Error **errp);
/* PCI中断引脚交换函数：0 <= pin <= 3，0 = INTA, 1 = INTB, 2 = INTC, 3 = INTD */
static inline int pci_swizzle(int slot, int pin)
{
    return (slot + pin) % PCI_NUM_PINS;
}
/* PCI交换映射中断函数 */
int pci_swizzle_map_irq_fn(PCIDevice *pci_dev, int pin);
/* 注册PCI根总线 */
PCIBus *pci_register_root_bus(DeviceState *parent, const char *name,
                              pci_set_irq_fn set_irq, pci_map_irq_fn map_irq,
                              void *irq_opaque,
                              MemoryRegion *mem, MemoryRegion *io,
                              uint8_t devfn_min, int nirq,
                              const char *typename);
/* 注销PCI根总线 */
void pci_unregister_root_bus(PCIBus *bus);
/* 设置PCI总线中断路由函数 */
void pci_bus_set_route_irq_fn(PCIBus *, pci_route_irq_fn);
/* 将PCI设备传统中断路由到IRQ */
PCIINTxRoute pci_device_route_intx_to_irq(PCIDevice *dev, int pin);
/* 检查传统中断路由是否改变 */
bool pci_intx_route_changed(PCIINTxRoute *old, PCIINTxRoute *new);
/* 触发PCI总线传统中断路由通知器 */
void pci_bus_fire_intx_routing_notifier(PCIBus *bus);
/* 设置PCI设备传统中断路由通知器 */
                                          PCIINTxRoutingNotifier notifier);
/* 重置PCI设备 */
void pci_device_reset(PCIDevice *dev);

/* 初始化PCI总线上的网卡设备 */
void pci_init_nic_devices(PCIBus *bus, const char *default_model);
/* 在指定槽位初始化网卡设备 */
bool pci_init_nic_in_slot(PCIBus *rootbus, const char *default_model,
                          const char *alias, const char *devaddr);
/* 初始化PCI VGA设备 */
PCIDevice *pci_vga_init(PCIBus *bus);

/* 获取PCI设备所在的总线 */
static inline PCIBus *pci_get_bus(const PCIDevice *dev)
{
    return PCI_BUS(qdev_get_parent_bus(DEVICE(dev)));
}
/* 获取PCI总线号 */
int pci_bus_num(PCIBus *s);
/* 获取PCI总线范围 */
void pci_bus_range(PCIBus *bus, int *min_bus, int *max_bus);
/* 获取PCI设备的总线号 */
static inline int pci_dev_bus_num(const PCIDevice *dev)
{
    return pci_bus_num(pci_get_bus(dev));
}

/* 获取PCI总线的NUMA节点 */
int pci_bus_numa_node(PCIBus *bus);
/* 遍历指定总线上的每个设备 */
void pci_for_each_device(PCIBus *bus, int bus_num,
                         pci_bus_dev_fn fn,
                         void *opaque);
/* 反向遍历指定总线上的每个设备 */
void pci_for_each_device_reverse(PCIBus *bus, int bus_num,
                                 pci_bus_dev_fn fn,
                                 void *opaque);
/* 遍历总线下的每个设备 */
void pci_for_each_device_under_bus(PCIBus *bus,
                                   pci_bus_dev_fn fn, void *opaque);
/* 反向遍历总线下的每个设备 */
void pci_for_each_device_under_bus_reverse(PCIBus *bus,
                                           pci_bus_dev_fn fn,
                                           void *opaque);
/* 深度优先遍历每个总线 */
void pci_for_each_bus_depth_first(PCIBus *bus, pci_bus_ret_fn begin,
                                  pci_bus_fn end, void *parent_state);
/* 获取多功能设备的功能0 */
PCIDevice *pci_get_function_0(PCIDevice *pci_dev);

/* 当不需要特定扫描顺序时使用此包装器 */
static inline
void pci_for_each_bus(PCIBus *bus, pci_bus_fn fn, void *opaque)
{
    pci_for_each_bus_depth_first(bus, NULL, fn, opaque);
}

/* 获取PCI设备的根总线 */
PCIBus *pci_device_root_bus(const PCIDevice *d);
/* 获取PCI根总线路径 */
const char *pci_root_bus_path(PCIDevice *dev);
/* 检查PCI总线是否绕过IOMMU */
bool pci_bus_bypass_iommu(PCIBus *bus);
/* 查找PCI设备 */
PCIDevice *pci_find_device(PCIBus *bus, int bus_num, uint8_t devfn);
/* 通过ID查找PCI设备 */
int pci_qdev_find_device(const char *id, PCIDevice **pdev);
/* 获取PCI总线的64位窗口范围 */
void pci_bus_get_w64_range(PCIBus *bus, Range *range);

/* 取消断言PCI设备的传统中断 */
void pci_device_deassert_intx(PCIDevice *dev);

/* 页面请求接口 (Page Request Interface) */
typedef enum {
    IOMMU_PRI_RESP_SUCCESS,         /* PRI响应成功 */
    IOMMU_PRI_RESP_INVALID_REQUEST, /* PRI响应无效请求 */
    IOMMU_PRI_RESP_FAILURE,         /* PRI响应失败 */
} IOMMUPRIResponseCode;

/* IOMMU页面请求接口响应结构体 */
typedef struct IOMMUPRIResponse {
    IOMMUPRIResponseCode response_code; /* 响应代码 */
    uint16_t prgi;                      /* 页面请求组索引 */
} IOMMUPRIResponse;

struct IOMMUPRINotifier;

/* IOMMU PRI通知函数类型定义 */
typedef void (*IOMMUPRINotify)(struct IOMMUPRINotifier *notifier,
                               IOMMUPRIResponse *response);

/* IOMMU PRI通知器结构体 */
typedef struct IOMMUPRINotifier {
    IOMMUPRINotify notify; /* 通知函数指针 */
} IOMMUPRINotifier;

/* PCI PRI页面请求组索引掩码 */
#define PCI_PRI_PRGI_MASK 0x1ffU

/**
 * struct PCIIOMMUOps: PCI总线特定IOMMU处理器的回调结构体
 *
 * 允许为PCI总线上的一组设备修改PCI框架的某些IOMMU操作行为。
 */
typedef struct PCIIOMMUOps {
    /**
     * @get_address_space: 获取PCI总线上一组设备的地址空间
     *
     * 强制回调，返回指向#AddressSpace的指针
     *
     * @bus: 被访问的#PCIBus
     *
     * @opaque: 传递给pci_setup_iommu()的数据
     *
     * @devfn: 设备和功能号
     */
    AddressSpace * (*get_address_space)(PCIBus *bus, void *opaque, int devfn);
    /**
     * @set_iommu_device: 将HostIOMMUDevice附加到vIOMMU
     *
     * 可选回调，如果在vIOMMU中未实现，则vIOMMU无法
     * 从关联的HostIOMMUDevice检索主机信息。
     *
     * @bus: PCI设备的#PCIBus
     *
     * @opaque: 传递给pci_setup_iommu()的数据
     *
     * @devfn: PCI设备的设备和功能号
     *
     * @dev: 要附加的#HostIOMMUDevice
     *
     * @errp: 仅在返回false时传出Error
     *
     * 返回值: 如果HostIOMMUDevice已附加则返回true，否则返回false并设置errp
     */
    bool (*set_iommu_device)(PCIBus *bus, void *opaque, int devfn,
                             HostIOMMUDevice *dev, Error **errp);
    /**
     * @unset_iommu_device: 从vIOMMU分离HostIOMMUDevice
     *
     * 可选回调。
     *
     * @bus: PCI设备的#PCIBus
     *
     * @opaque: 传递给pci_setup_iommu()的数据
     *
     * @devfn: PCI设备的设备和功能号
     */
    void (*unset_iommu_device)(PCIBus *bus, void *opaque, int devfn);
    /**
     * @get_iotlb_info: 获取初始化设备IOTLB所需的属性
     *
     * 如果允许设备缓存转换，则需要此回调。
     *
     * @opaque: 传递给pci_setup_iommu()的数据
     *
     * @addr_width: IOMMU的地址宽度（输出参数）
     *
     * @min_page_size: IOMMU的页面大小（输出参数）
     */
    void (*get_iotlb_info)(void *opaque, uint8_t *addr_width,
                           uint32_t *min_page_size);
    /**
     * @init_iotlb_notifier: 初始化IOMMU通知器
     *
     * 可选回调。
     *
     * @bus: PCI设备的#PCIBus
     *
     * @opaque: 传递给pci_setup_iommu()的数据
     *
     * @devfn: PCI设备的设备和功能号
     *
     * @n: 要初始化的通知器
     *
     * @fn: 要安装的回调函数
     *
     * @user_opaque: 可用于跟踪状态的用户指针
     */
    void (*init_iotlb_notifier)(PCIBus *bus, void *opaque, int devfn,
                                IOMMUNotifier *n, IOMMUNotify fn,
                                void *user_opaque);
    /**
     * @register_iotlb_notifier: 设置IOTLB失效通知器
     *
     * 如果允许设备缓存转换，则需要此回调。
     *
     * @bus: PCI设备的#PCIBus
     *
     * @opaque: 传递给pci_setup_iommu()的数据
     *
     * @devfn: PCI设备的设备和功能号
     *
     * @pasid: 要监视的地址空间的pasid
     *
     * @n: 要注册的通知器
     */
    void (*register_iotlb_notifier)(PCIBus *bus, void *opaque, int devfn,
                                    uint32_t pasid, IOMMUNotifier *n);
    /**
     * @unregister_iotlb_notifier: 移除IOTLB失效通知器
     *
     * 如果允许设备缓存转换，则需要此回调。
     *
     * @bus: PCI设备的#PCIBus
     *
     * @opaque: 传递给pci_setup_iommu()的数据
     *
     * @devfn: PCI设备的设备和功能号
     *
     * @pasid: 要停止监视的地址空间的pasid
     *
     * @n: 要注销的通知器
     */
    void (*unregister_iotlb_notifier)(PCIBus *bus, void *opaque, int devfn,
                                      uint32_t pasid, IOMMUNotifier *n);
    /**
     * @ats_request_translation: 发出ATS请求
     *
     * 如果允许设备使用地址转换服务，则需要此回调。
     *
     * @bus: PCI设备的#PCIBus
     *
     * @opaque: 传递给pci_setup_iommu()的数据
     *
     * @devfn: PCI设备的设备和功能号
     *
     * @pasid: 用于请求的地址空间的pasid
     *
     * @priv_req: 特权模式位（PASID TLP）
     *
     * @exec_req: 执行请求位（PASID TLP）
     *
     * @addr: 要转换的内存范围的起始地址
     *
     * @length: 内存范围的长度（字节）
     *
     * @no_write: 请求只读转换（如果支持）
     *
     * @result: 存储TLB条目的缓冲区
     *
     * @result_length: 结果缓冲区长度
     *
     * @err_count: 未转换子区域的数量
     *
     * 返回值: 存储在结果缓冲区中的转换数量，或
     * 如果缓冲区不够大则返回-ENOMEM
     */
    ssize_t (*ats_request_translation)(PCIBus *bus, void *opaque, int devfn,
                                       uint32_t pasid, bool priv_req,
                                       bool exec_req, hwaddr addr,
                                       size_t length, bool no_write,
                                       IOMMUTLBEntry *result,
                                       size_t result_length,
                                       uint32_t *err_count);
    /**
     * @pri_register_notifier: 设置PRI完成回调
     *
     * 如果允许设备使用页面请求接口，则需要此回调。
     *
     * @bus: PCI设备的#PCIBus
     *
     * @opaque: 传递给pci_setup_iommu()的数据
     *
     * @devfn: PCI设备的设备和功能号
     *
     * @pasid: 要跟踪的地址空间的pasid
     *
     * @notifier: 要注册的通知器
     */
    void (*pri_register_notifier)(PCIBus *bus, void *opaque, int devfn,
                                  uint32_t pasid, IOMMUPRINotifier *notifier);
    /**
     * @pri_unregister_notifier: 移除PRI完成回调
     *
     * 如果允许设备使用页面请求接口，则需要此回调。
     *
     * @bus: PCI设备的#PCIBus
     *
     * @opaque: 传递给pci_setup_iommu()的数据
     *
     * @devfn: PCI设备的设备和功能号
     *
     * @pasid: 要停止跟踪的地址空间的pasid
     */
    void (*pri_unregister_notifier)(PCIBus *bus, void *opaque, int devfn,
                                    uint32_t pasid);
    /**
     * @pri_request_page: 发出PRI请求
     *
     * 如果允许设备使用页面请求接口，则需要此回调。
     *
     * @bus: PCI设备的#PCIBus
     *
     * @opaque: 传递给pci_setup_iommu()的数据
     *
     * @devfn: PCI设备的设备和功能号
     *
     * @pasid: 用于请求的地址空间的pasid
     *
     * @priv_req: 特权模式位（PASID TLP）
     *
     * @exec_req: 执行请求位（PASID TLP）
     *
     * @addr: 请求页面的未转换地址
     *
     * @lpig: 组中的最后一页
     *
     * @prgi: 页面请求组索引
     *
     * @is_read: 请求读访问
     *
     * @is_write: 请求写访问
     */
    int (*pri_request_page)(PCIBus *bus, void *opaque, int devfn,
                            uint32_t pasid, bool priv_req, bool exec_req,
                            hwaddr addr, bool lpig, uint16_t prgi, bool is_read,
                            bool is_write);
} PCIIOMMUOps;

/* 获取PCI设备的IOMMU地址空间 */
AddressSpace *pci_device_iommu_address_space(PCIDevice *dev);
/* 为PCI设备设置IOMMU设备 */
bool pci_device_set_iommu_device(PCIDevice *dev, HostIOMMUDevice *hiod,
                                 Error **errp);
/* 取消设置PCI设备的IOMMU设备 */
void pci_device_unset_iommu_device(PCIDevice *dev);

/**
 * pci_iommu_get_iotlb_info: 获取初始化设备IOTLB所需的属性
 *
 * 成功时返回0，否则返回负的errno值。
 *
 * @dev: 想要获取信息的设备
 * @addr_width: IOMMU的地址宽度（输出参数）
 * @min_page_size: IOMMU的页面大小（输出参数）
 */
int pci_iommu_get_iotlb_info(PCIDevice *dev, uint8_t *addr_width,
                             uint32_t *min_page_size);

/**
 * pci_iommu_init_iotlb_notifier: 初始化IOMMU通知器
 *
 * 此函数在注册IOTLB通知器之前由设备使用。
 *
 * @dev: 设备
 * @n: 要初始化的通知器
 * @fn: 要安装的回调函数
 * @opaque: 可用于跟踪状态的用户指针
 */
int pci_iommu_init_iotlb_notifier(PCIDevice *dev, IOMMUNotifier *n,
                                  IOMMUNotify fn, void *opaque);

/**
 * pci_ats_request_translation: 执行ATS请求
 *
 * 成功时返回存储在@result中的转换数量，
 * 否则返回负的错误代码。
 * 当结果缓冲区不够大无法存储所有转换时返回-ENOMEM。
 *
 * @dev: 支持ATS的PCI设备
 * @pasid: 将在其中进行转换的地址空间的pasid
 * @priv_req: 特权模式位（PASID TLP）
 * @exec_req: 执行请求位（PASID TLP）
 * @addr: 要转换的内存范围的起始地址
 * @length: 内存范围的长度（字节）
 * @no_write: 请求只读转换（如果支持）
 * @result: 存储TLB条目的缓冲区
 * @result_length: 结果缓冲区长度
 * @err_count: 未转换子区域的数量
 */
ssize_t pci_ats_request_translation(PCIDevice *dev, uint32_t pasid,
                                    bool priv_req, bool exec_req,
                                    hwaddr addr, size_t length,
                                    bool no_write, IOMMUTLBEntry *result,
                                    size_t result_length,
                                    uint32_t *err_count);

/**
 * pci_pri_request_page: 执行PRI请求
 *
 * 如果PRI请求已发送到客户操作系统则返回0，
 * 否则返回错误代码。
 *
 * @dev: 支持PRI的PCI设备
 * @pasid: 将在其中进行转换的地址空间的pasid
 * @priv_req: 特权模式位（PASID TLP）
 * @exec_req: 执行请求位（PASID TLP）
 * @addr: 请求页面的未转换地址
 * @lpig: 组中的最后一页
 * @prgi: 页面请求组索引
 * @is_read: 请求读访问
 * @is_write: 请求写访问
 */
int pci_pri_request_page(PCIDevice *dev, uint32_t pasid, bool priv_req,
                         bool exec_req, hwaddr addr, bool lpig,
                         uint16_t prgi, bool is_read, bool is_write);

/**
 * pci_pri_register_notifier: 为给定地址空间注册PRI回调
 *
 * 成功时返回0，否则返回错误代码。
 *
 * @dev: 支持PRI的PCI设备
 * @pasid: 要跟踪的地址空间的pasid
 * @notifier: 要注册的通知器
 */
int pci_pri_register_notifier(PCIDevice *dev, uint32_t pasid,
                              IOMMUPRINotifier *notifier);

/**
 * pci_pri_unregister_notifier: 从给定地址空间移除PRI回调
 *
 * @dev: 支持PRI的PCI设备
 * @pasid: 要停止跟踪的地址空间的pasid
 */
void pci_pri_unregister_notifier(PCIDevice *dev, uint32_t pasid);

/**
 * pci_iommu_register_iotlb_notifier: 为特定地址空间中IOMMU转换条目的
 * 变化注册通知器
 *
 * 成功时返回0，否则返回负的errno值。
 *
 * @dev: 想要获得通知的设备
 * @pasid: 要跟踪的地址空间的pasid
 * @n: 要注册的通知器
 */
int pci_iommu_register_iotlb_notifier(PCIDevice *dev, uint32_t pasid,
                                      IOMMUNotifier *n);

/**
 * pci_iommu_unregister_iotlb_notifier: 注销已通过
 * pci_iommu_register_iotlb_notifier注册的通知器
 *
 * 成功时返回0，否则返回负的errno值。
 *
 * @dev: 想要停止通知的设备
 * @pasid: 要停止跟踪的地址空间的pasid
 * @n: 要注销的通知器
 */
int pci_iommu_unregister_iotlb_notifier(PCIDevice *dev, uint32_t pasid,
                                        IOMMUNotifier *n);

/**
 * pci_setup_iommu: 为PCIBus初始化特定的IOMMU处理器
 *
 * 让PCI主机桥定义特定的操作。
 *
 * @bus: 正在更新的#PCIBus
 * @ops: #PCIIOMMUOps操作结构
 * @opaque: 传递给@ops结构回调的不透明数据
 */
void pci_setup_iommu(PCIBus *bus, const PCIIOMMUOps *ops, void *opaque);

/* 计算PCI BAR地址 */
pcibus_t pci_bar_address(PCIDevice *d,
                         int reg, uint8_t type, pcibus_t size);

/* 设置PCI配置空间中的单字节值 */
static inline void
pci_set_byte(uint8_t *config, uint8_t val)
{
    *config = val;
}

/* 获取PCI配置空间中的单字节值 */
static inline uint8_t
pci_get_byte(const uint8_t *config)
{
    return *config;
}

/* 设置PCI配置空间中的16位字值（小端序） */
static inline void
pci_set_word(uint8_t *config, uint16_t val)
{
    stw_le_p(config, val);
}

/* 获取PCI配置空间中的16位字值（小端序） */
static inline uint16_t
pci_get_word(const uint8_t *config)
{
    return lduw_le_p(config);
}

/* 设置PCI配置空间中的32位长字值（小端序） */
static inline void
pci_set_long(uint8_t *config, uint32_t val)
{
    stl_le_p(config, val);
}

/* 获取PCI配置空间中的32位长字值（小端序） */
static inline uint32_t
pci_get_long(const uint8_t *config)
{
    return ldl_le_p(config);
}

/*
 * PCI能力和/或其字段通常只按DWORD对齐，
 * 因此pci_set/get_quad()使用的机制
 * must be tolerant to unaligned pointers
 *
 */
static inline void
pci_set_quad(uint8_t *config, uint64_t val)
{
    stq_le_p(config, val);
}

static inline uint64_t
pci_get_quad(const uint8_t *config)
{
    return ldq_le_p(config);
}

static inline void
pci_config_set_vendor_id(uint8_t *pci_config, uint16_t val)
{
    pci_set_word(&pci_config[PCI_VENDOR_ID], val);
}

static inline void
pci_config_set_device_id(uint8_t *pci_config, uint16_t val)
{
    pci_set_word(&pci_config[PCI_DEVICE_ID], val);
}

static inline void
pci_config_set_revision(uint8_t *pci_config, uint8_t val)
{
    pci_set_byte(&pci_config[PCI_REVISION_ID], val);
}

static inline void
pci_config_set_class(uint8_t *pci_config, uint16_t val)
{
    pci_set_word(&pci_config[PCI_CLASS_DEVICE], val);
}

static inline void
pci_config_set_prog_interface(uint8_t *pci_config, uint8_t val)
{
    pci_set_byte(&pci_config[PCI_CLASS_PROG], val);
}

static inline void
pci_config_set_interrupt_pin(uint8_t *pci_config, uint8_t val)
{
    pci_set_byte(&pci_config[PCI_INTERRUPT_PIN], val);
}

/*
 * helper functions to do bit mask operation on configuration space.
 * Just to set bit, use test-and-set and discard returned value.
 * Just to clear bit, use test-and-clear and discard returned value.
 * NOTE: They aren't atomic.
 */
static inline uint8_t
pci_byte_test_and_clear_mask(uint8_t *config, uint8_t mask)
{
    uint8_t val = pci_get_byte(config);
    pci_set_byte(config, val & ~mask);
    return val & mask;
}

static inline uint8_t
pci_byte_test_and_set_mask(uint8_t *config, uint8_t mask)
{
    uint8_t val = pci_get_byte(config);
    pci_set_byte(config, val | mask);
    return val & mask;
}

static inline uint16_t
pci_word_test_and_clear_mask(uint8_t *config, uint16_t mask)
{
    uint16_t val = pci_get_word(config);
    pci_set_word(config, val & ~mask);
    return val & mask;
}

static inline uint16_t
pci_word_test_and_set_mask(uint8_t *config, uint16_t mask)
{
    uint16_t val = pci_get_word(config);
    pci_set_word(config, val | mask);
    return val & mask;
}

static inline uint32_t
pci_long_test_and_clear_mask(uint8_t *config, uint32_t mask)
{
    uint32_t val = pci_get_long(config);
    pci_set_long(config, val & ~mask);
    return val & mask;
}

static inline uint32_t
pci_long_test_and_set_mask(uint8_t *config, uint32_t mask)
{
    uint32_t val = pci_get_long(config);
    pci_set_long(config, val | mask);
    return val & mask;
}

static inline uint64_t
pci_quad_test_and_clear_mask(uint8_t *config, uint64_t mask)
{
    uint64_t val = pci_get_quad(config);
    pci_set_quad(config, val & ~mask);
    return val & mask;
}

static inline uint64_t
pci_quad_test_and_set_mask(uint8_t *config, uint64_t mask)
{
    uint64_t val = pci_get_quad(config);
    pci_set_quad(config, val | mask);
    return val & mask;
}

/* Access a register specified by a mask */
static inline void
pci_set_byte_by_mask(uint8_t *config, uint8_t mask, uint8_t reg)
{
    uint8_t val = pci_get_byte(config);
    uint8_t rval;

    assert(mask);
    rval = reg << ctz32(mask);
    pci_set_byte(config, (~mask & val) | (mask & rval));
}

static inline void
pci_set_word_by_mask(uint8_t *config, uint16_t mask, uint16_t reg)
{
    uint16_t val = pci_get_word(config);
    uint16_t rval;

    assert(mask);
    rval = reg << ctz32(mask);
    pci_set_word(config, (~mask & val) | (mask & rval));
}

static inline void
pci_set_long_by_mask(uint8_t *config, uint32_t mask, uint32_t reg)
{
    uint32_t val = pci_get_long(config);
    uint32_t rval;

    assert(mask);
    rval = reg << ctz32(mask);
    pci_set_long(config, (~mask & val) | (mask & rval));
}

static inline void
pci_set_quad_by_mask(uint8_t *config, uint64_t mask, uint64_t reg)
{
    uint64_t val = pci_get_quad(config);
    uint64_t rval;

    assert(mask);
    rval = reg << ctz32(mask);
    pci_set_quad(config, (~mask & val) | (mask & rval));
}

PCIDevice *pci_new_multifunction(int devfn, const char *name);
PCIDevice *pci_new(int devfn, const char *name);
bool pci_realize_and_unref(PCIDevice *dev, PCIBus *bus, Error **errp);

PCIDevice *pci_create_simple_multifunction(PCIBus *bus, int devfn,
                                           const char *name);
PCIDevice *pci_create_simple(PCIBus *bus, int devfn, const char *name);

void lsi53c8xx_handle_legacy_cmdline(DeviceState *lsi_dev);

qemu_irq pci_allocate_irq(PCIDevice *pci_dev);
void pci_set_irq(PCIDevice *pci_dev, int level);
int pci_irq_disabled(PCIDevice *d);

static inline void pci_irq_assert(PCIDevice *pci_dev)
{
    pci_set_irq(pci_dev, 1);
}

static inline void pci_irq_deassert(PCIDevice *pci_dev)
{
    pci_set_irq(pci_dev, 0);
}

MSIMessage pci_get_msi_message(PCIDevice *dev, int vector);
void pci_set_enabled(PCIDevice *pci_dev, bool state);
void pci_set_power(PCIDevice *pci_dev, bool state);
int pci_pm_init(PCIDevice *pci_dev, uint8_t offset, Error **errp);

#endif
