/* 防止头文件重复包含的宏定义开始 */
#ifndef QEMU_PCI_DEVICE_H
/* 定义头文件保护宏 */
#define QEMU_PCI_DEVICE_H

/* 包含PCI基础功能头文件 */
#include "hw/pci/pci.h"
/* 包含PCI Express相关功能头文件 */
#include "hw/pci/pcie.h"
/* 包含PCI Express DOE (Data Object Exchange)功能头文件 */
#include "hw/pci/pcie_doe.h"

/* 定义PCI设备的类型字符串标识符 */
#define TYPE_PCI_DEVICE "pci-device"
/* 前向声明PCIDeviceClass结构体 */
typedef struct PCIDeviceClass PCIDeviceClass;
/* 声明QEMU对象系统的类型检查器宏，用于PCIDevice和PCIDeviceClass */
DECLARE_OBJ_CHECKERS(PCIDevice, PCIDeviceClass,
                     PCI_DEVICE, TYPE_PCI_DEVICE)

/*
 * 定义可插入CXL总线的设备接口。在规范中，这实际上是"CXL组件"，
 * 但我们将其命名为设备以匹配PCI命名约定。
 */
#define INTERFACE_CXL_DEVICE "cxl-device"

/* 定义可插入PCI Express总线的设备接口 */
#define INTERFACE_PCIE_DEVICE "pci-express-device"

/* 定义可插入传统PCI总线的设备接口 */
#define INTERFACE_CONVENTIONAL_PCI_DEVICE "conventional-pci-device"

/* PCI设备类结构体定义 */
struct PCIDeviceClass {
    /* 继承自DeviceClass的父类 */
    DeviceClass parent_class;

    /* 设备实现函数指针，用于设备初始化 */
    void (*realize)(PCIDevice *dev, Error **errp);
    /* 设备退出/清理函数指针 */
    PCIUnregisterFunc *exit;
    /* PCI配置空间读取函数指针 */
    PCIConfigReadFunc *config_read;
    /* PCI配置空间写入函数指针 */
    PCIConfigWriteFunc *config_write;

    /* PCI厂商ID */
    uint16_t vendor_id;
    /* PCI设备ID */
    uint16_t device_id;
    /* 设备版本号 */
    uint8_t revision;
    /* PCI设备类别ID */
    uint16_t class_id;
    /* 子系统厂商ID（仅适用于头部类型0） */
    uint16_t subsystem_vendor_id;       /* only for header type = 0 */
    /* 子系统ID（仅适用于头部类型0） */
    uint16_t subsystem_id;              /* only for header type = 0 */

    /* ROM文件路径（用于ROM BAR） */
    const char *romfile;                /* rom bar */

    /* SR-IOV虚拟功能是否可由用户创建 */
    bool sriov_vf_user_creatable;
};

/* PCI请求ID类型枚举 */
enum PCIReqIDType {
    /* 无效的请求ID类型 */
    PCI_REQ_ID_INVALID = 0,
    /* 总线设备功能号(Bus Device Function)类型 */
    PCI_REQ_ID_BDF,
    /* 次级总线类型 */
    PCI_REQ_ID_SECONDARY_BUS,
    /* 最大值标记 */
    PCI_REQ_ID_MAX,
};
/* 定义PCIReqIDType类型别名 */
typedef enum PCIReqIDType PCIReqIDType;

/* PCI请求ID缓存结构体 */
struct PCIReqIDCache {
    /* 指向PCI设备的指针 */
    PCIDevice *dev;
    /* 请求ID的类型 */
    PCIReqIDType type;
};
/* 定义PCIReqIDCache类型别名 */
typedef struct PCIReqIDCache PCIReqIDCache;

/* PCI设备结构体定义 */
struct PCIDevice {
    /* QEMU设备状态基类 */
    DeviceState qdev;
    /* 标识设备是否部分热插拔 */
    bool partially_hotplugged;
    /* 标识设备是否已启用 */
    bool enabled;

    /* PCI配置空间指针 */
    uint8_t *config;

    /*
     * 用于在加载时启用配置检查。注意即使在cmask中设置，可写位也不会被检查
     */
    uint8_t *cmask;

    /* 用于实现读写字节 */
    uint8_t *wmask;

    /* 用于实现RW1C(写1清零)字节 */
    uint8_t *w1cmask;

    /* 用于为能力分配配置空间 */
    uint8_t *used;

    /* 以下字段为只读 */
    /* 设备功能号(Device Function Number) */
    int32_t devfn;
    /*
     * 缓存的设备用于获取请求者ID，避免每次调用PCI请求(如MSI)时遍历PCI树。
     * 对于传统PCI根复合体，此字段无意义。
     */
    PCIReqIDCache requester_id_cache;
    /* 设备名称字符串 */
    char name[64];
    /* PCI I/O区域数组 */
    PCIIORegion io_regions[PCI_NUM_REGIONS];
    /* 总线主控地址空间 */
    AddressSpace bus_master_as;
    /* 标识是否为总线主控设备 */
    bool is_master;
    /* 总线主控容器内存区域 */
    MemoryRegion bus_master_container_region;
    /* 总线主控使能内存区域 */
    MemoryRegion bus_master_enable_region;

    /* 不要访问以下字段 */
    /* PCI配置读取函数指针 */
    PCIConfigReadFunc *config_read;
    /* PCI配置写入函数指针 */
    PCIConfigWriteFunc *config_write;

    /* 传统PCI VGA区域 */
    MemoryRegion *vga_regions[QEMU_PCI_VGA_NUM_REGIONS];
    /* 标识是否具有VGA功能 */
    bool has_vga;

    /* 当前IRQ电平。由通用PCI代码内部使用 */
    uint8_t irq_state;

    /* 能力位标志 */
    uint32_t cap_present;

    /* PM能力在配置空间中的偏移 */
    uint8_t pm_cap;

    /* MSI-X能力在配置空间中的偏移 */
    uint8_t msix_cap;

    /* MSI-X条目数量 */
    int msix_entries_nr;

    /* 存储MSI-X表和待处理位数组的空间 */
    uint8_t *msix_table;
    uint8_t *msix_pba;

    /* 在中断通知期间可能被INTx或MSI使用 */
    void *irq_opaque;

    /* MSI触发函数指针 */
    MSITriggerFunc *msi_trigger;
    /* MSI准备消息函数指针 */
    MSIPrepareMessageFunc *msi_prepare_message;
    /* MSI-X准备消息函数指针 */
    MSIxPrepareMessageFunc *msix_prepare_message;

    /* MSI-X独占BAR设置的内存区域容器 */
    MemoryRegion msix_exclusive_bar;
    /* MSI-X表和待处理位条目的内存区域 */
    MemoryRegion msix_table_mmio;
    MemoryRegion msix_pba_mmio;
    /* 驱动程序实际使用的条目引用计数 */
    unsigned *msix_entry_used;
    /* MSI-X功能掩码设置或MSI-X禁用 */
    bool msix_function_masked;
    /* VMState所需的版本ID */
    int32_t version_id;

    /* MSI能力在配置空间中的偏移 */
    uint8_t msi_cap;

    /* PCI Express设备 */
    PCIExpressDevice exp;

    /* SHPC(标准热插拔控制器)设备 */
    SHPCDevice *shpc;

    /* 选项ROM的位置 */
    char *romfile;
    /* ROM大小 */
    uint32_t romsize;
    /* 标识是否有ROM */
    bool has_rom;
    /* ROM内存区域 */
    MemoryRegion rom;
    /* ROM BAR编号 */
    int32_t rom_bar;

    /* INTx路由通知器 */
    PCIINTxRoutingNotifier intx_routing_notifier;

    /* MSI-X通知器 */
    MSIVectorUseNotifier msix_vector_use_notifier;
    MSIVectorReleaseNotifier msix_vector_release_notifier;
    MSIVectorPollNotifier msix_vector_poll_notifier;

    /* SPDM(安全协议和数据模型)端口 */
    uint16_t spdm_port;

    /* DOE(数据对象交换)能力 */
    DOECap doe_spdm;

    /* 网络故障转移对中备用设备的ID */
    char *failover_pair_id;
    /* ACPI索引 */
    uint32_t acpi_index;

    /*
     * 为设备配置的间接DMA区域反弹缓冲区大小。这是一个配置参数，
     * 在实现设备时会反映到bus_master_as中。
     */
    uint32_t max_bounce_buffer_size;

    /* SR-IOV物理功能设备名称 */
    char *sriov_pf;
};

/* 获取PCI设备的INTx中断引脚号 */
static inline int pci_intx(PCIDevice *pci_dev)
{
    /* 从配置空间读取中断引脚并减1(因为引脚从1开始编号) */
    return pci_get_byte(pci_dev->config + PCI_INTERRUPT_PIN) - 1;
}

/* 检查PCI设备是否为CXL设备 */
static inline int pci_is_cxl(const PCIDevice *d)
{
    /* 检查能力位中是否包含CXL能力 */
    return d->cap_present & QEMU_PCIE_CAP_CXL;
}

/* 检查PCI设备是否为PCI Express设备 */
static inline int pci_is_express(const PCIDevice *d)
{
    /* 检查能力位中是否包含Express能力 */
    return d->cap_present & QEMU_PCI_CAP_EXPRESS;
}

/* 检查PCI设备是否为Express下游端口 */
static inline int pci_is_express_downstream_port(const PCIDevice *d)
{
    uint8_t type;

    /* 如果不是Express设备或没有Express能力，返回0 */
    if (!pci_is_express(d) || !d->exp.exp_cap) {
        return 0;
    }

    /* 获取PCIe设备类型 */
    type = pcie_cap_get_type(d);

    /* 检查是否为下游端口或根端口 */
    return type == PCI_EXP_TYPE_DOWNSTREAM || type == PCI_EXP_TYPE_ROOT_PORT;
}

/* 检查PCI设备是否为虚拟功能(VF) */
static inline int pci_is_vf(const PCIDevice *d)
{
    /* 检查是否有SR-IOV物理功能或Express SR-IOV虚拟功能 */
    return d->sriov_pf || d->exp.sriov_vf.pf != NULL;
}

/* 获取PCI设备配置空间大小 */
static inline uint32_t pci_config_size(const PCIDevice *d)
{
    /* 如果是Express设备返回PCIe配置空间大小，否则返回PCI配置空间大小 */
    return pci_is_express(d) ? PCIE_CONFIG_SPACE_SIZE : PCI_CONFIG_SPACE_SIZE;
}

/* 获取PCI设备的BDF(总线设备功能)号 */
static inline uint16_t pci_get_bdf(PCIDevice *dev)
{
    /* 构建BDF：总线号和设备功能号 */
    return PCI_BUILD_BDF(pci_bus_num(pci_get_bus(dev)), dev->devfn);
}

/* 获取PCI设备的请求者ID */
uint16_t pci_requester_id(PCIDevice *dev);

/* DMA访问函数 */
/* 获取PCI设备的地址空间 */
static inline AddressSpace *pci_get_address_space(PCIDevice *dev)
{
    /* 返回设备的总线主控地址空间 */
    return &dev->bus_master_as;
}

/**
 * pci_dma_rw: 从PCI设备读取或写入地址空间
 *
 * 返回MemTxResult指示操作是否成功或失败
 * (例如未分配内存、设备拒绝事务、IOMMU故障)
 *
 * @dev: 执行内存访问的#PCIDevice
 * @addr: #PCIDevice地址空间内的地址
 * @buf: 传输数据的缓冲区
 * @len: 要读取或写入的字节数
 * @dir: 指示传输方向
 */
static inline MemTxResult pci_dma_rw(PCIDevice *dev, dma_addr_t addr,
                                     void *buf, dma_addr_t len,
                                     DMADirection dir, MemTxAttrs attrs)
{
    /* 调用DMA内存读写函数 */
    return dma_memory_rw(pci_get_address_space(dev), addr, buf, len,
                         dir, attrs);
}

/**
 * pci_dma_read: 从PCI设备读取地址空间
 *
 * 返回MemTxResult指示操作是否成功或失败
 * (例如未分配内存、设备拒绝事务、IOMMU故障)。在RCU临界区内调用。
 *
 * @dev: 执行内存访问的#PCIDevice
 * @addr: #PCIDevice地址空间内的地址
 * @buf: 传输数据的缓冲区
 * @len: 传输数据的长度
 */
static inline MemTxResult pci_dma_read(PCIDevice *dev, dma_addr_t addr,
                                       void *buf, dma_addr_t len)
{
    /* 调用DMA读写函数，方向为到设备 */
    return pci_dma_rw(dev, addr, buf, len,
                      DMA_DIRECTION_TO_DEVICE, MEMTXATTRS_UNSPECIFIED);
}

/**
 * pci_dma_write: 从PCI设备写入地址空间
 *
 * 返回MemTxResult指示操作是否成功或失败
 * (例如未分配内存、设备拒绝事务、IOMMU故障)
 *
 * @dev: 执行内存访问的#PCIDevice
 * @addr: #PCIDevice地址空间内的地址
 * @buf: 传输数据的缓冲区
 * @len: 要写入的字节数
 */
static inline MemTxResult pci_dma_write(PCIDevice *dev, dma_addr_t addr,
                                        const void *buf, dma_addr_t len)
{
    /* 调用DMA读写函数，方向为从设备 */
    return pci_dma_rw(dev, addr, (void *) buf, len,
                      DMA_DIRECTION_FROM_DEVICE, MEMTXATTRS_UNSPECIFIED);
}

/* 定义PCI DMA加载/存储函数的宏 */
#define PCI_DMA_DEFINE_LDST(_l, _s, _bits) \
    /* 定义PCI DMA加载函数 */ \
    static inline MemTxResult ld##_l##_pci_dma(PCIDevice *dev, \
                                               dma_addr_t addr, \
                                               uint##_bits##_t *val, \
                                               MemTxAttrs attrs) \
    { \
        /* 调用对应的DMA加载函数 */ \
        return ld##_l##_dma(pci_get_address_space(dev), addr, val, attrs); \
    } \
    /* 定义PCI DMA存储函数 */ \
    static inline MemTxResult st##_s##_pci_dma(PCIDevice *dev, \
                                               dma_addr_t addr, \
                                               uint##_bits##_t val, \
                                               MemTxAttrs attrs) \
    { \
        /* 调用对应的DMA存储函数 */ \
        return st##_s##_dma(pci_get_address_space(dev), addr, val, attrs); \
    }

/* 定义8位无符号字节的加载/存储函数 */
PCI_DMA_DEFINE_LDST(ub, b, 8);
/* 定义16位小端无符号字的加载/存储函数 */
PCI_DMA_DEFINE_LDST(uw_le, w_le, 16)
/* 定义32位小端长字的加载/存储函数 */
PCI_DMA_DEFINE_LDST(l_le, l_le, 32);
/* 定义64位小端四字的加载/存储函数 */
PCI_DMA_DEFINE_LDST(q_le, q_le, 64);
/* 定义16位大端无符号字的加载/存储函数 */
PCI_DMA_DEFINE_LDST(uw_be, w_be, 16)
/* 定义32位大端长字的加载/存储函数 */
PCI_DMA_DEFINE_LDST(l_be, l_be, 32);
/* 定义64位大端四字的加载/存储函数 */
PCI_DMA_DEFINE_LDST(q_be, q_be, 64);

/* 取消定义宏 */
#undef PCI_DMA_DEFINE_LDST

/**
 * pci_dma_map: 将设备PCI地址空间范围映射到主机虚拟地址
 * @dev: 要访问的#PCIDevice
 * @addr: 该设备地址空间内的地址
 * @plen: 缓冲区长度的指针；返回时更新以指示是否只映射了请求范围的子集
 * @dir: 指示传输方向
 *
 * 返回: 主机指针，如果执行映射所需的资源耗尽则返回%NULL
 *       (在这种情况下*@plen被设置为零)
 */
static inline void *pci_dma_map(PCIDevice *dev, dma_addr_t addr,
                                dma_addr_t *plen, DMADirection dir)
{
    /* 调用DMA内存映射函数 */
    return dma_memory_map(pci_get_address_space(dev), addr, plen, dir,
                          MEMTXATTRS_UNSPECIFIED);
}

/* 取消PCI DMA映射 */
static inline void pci_dma_unmap(PCIDevice *dev, void *buffer, dma_addr_t len,
                                 DMADirection dir, dma_addr_t access_len)
{
    /* 调用DMA内存取消映射函数 */
    dma_memory_unmap(pci_get_address_space(dev), buffer, len, dir, access_len);
}

/* 初始化PCI DMA散列表 */
static inline void pci_dma_sglist_init(QEMUSGList *qsg, PCIDevice *dev,
                                       int alloc_hint)
{
    /* 初始化QEMU散列表 */
    qemu_sglist_init(qsg, DEVICE(dev), alloc_hint, pci_get_address_space(dev));
}

/* 外部声明PCI设备的VM状态描述 */
extern const VMStateDescription vmstate_pci_device;

/* 定义PCI设备VM状态宏 */
#define VMSTATE_PCI_DEVICE(_field, _state) {                         \
    .name       = (stringify(_field)),                               \
    .size       = sizeof(PCIDevice),                                 \
    .vmsd       = &vmstate_pci_device,                               \
    .flags      = VMS_STRUCT,                                        \
    .offset     = vmstate_offset_value(_state, _field, PCIDevice),   \
}

/* 定义PCI设备指针VM状态宏 */
#define VMSTATE_PCI_DEVICE_POINTER(_field, _state) {                 \
    .name       = (stringify(_field)),                               \
    .size       = sizeof(PCIDevice),                                 \
    .vmsd       = &vmstate_pci_device,                               \
    .flags      = VMS_STRUCT | VMS_POINTER,                          \
    .offset     = vmstate_offset_pointer(_state, _field, PCIDevice), \
}

/* 头文件保护宏结束 */
#endif
